{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"small_rng\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 17281646005809502401, "deps": [[1573238666360410412, "rand_chacha", false, 9969000999809520561], [18130209639506977569, "rand_core", false, 5057981731956045253]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-4788e137d3663320\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}